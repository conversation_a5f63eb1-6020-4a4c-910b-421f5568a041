/**
 * @file led_button_task.h
 * @brief LED和按键控制FreeRTOS任务头文件
 */

#ifndef LED_BUTTON_TASK_H
#define LED_BUTTON_TASK_H

#include "led_button_control.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief 启动LED和按键控制任务
 * @return true 启动成功, false 启动失败
 */
bool led_button_task_start(void);

/**
 * @brief 停止LED和按键控制任务
 */
void led_button_task_stop(void);

/**
 * @brief 检查LED和按键控制任务是否正在运行
 * @return true 正在运行, false 未运行
 */
bool led_button_task_is_running(void);

/**
 * @brief 设置设备配网状态（任务安全版本）
 * @param configured true=已配网, false=未配网
 */
void led_button_task_set_device_state(bool configured);

/**
 * @brief 获取当前按键状态（任务安全版本）
 * @return button_state_t 当前按键状态
 */
button_state_t led_button_task_get_state(void);

/**
 * @brief 检查是否正在进行工厂重置（任务安全版本）
 * @return true=正在工厂重置, false=正常状态
 */
bool led_button_task_is_factory_resetting(void);

#ifdef __cplusplus
}
#endif

#endif /* LED_BUTTON_TASK_H */
