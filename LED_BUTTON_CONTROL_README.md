# LED和按键控制系统

## 功能概述

本项目实现了一个完整的LED和按键控制系统，支持以下功能：

### 硬件配置
- **按键**: GPIO6, GPIO7
- **LED**: WS2812B LED (GPIO10)
- **输出控制**: GPIO5, GPIO1

### 主要功能

#### 1. LED状态指示
- **红色呼吸灯**: 设备未配网状态
- **白色呼吸灯**: 设备已配网状态
- **绿色常亮**: GPIO6激活状态 (GPIO5=HIGH, GPIO1=LOW)
- **蓝色常亮**: GPIO7激活状态 (GPIO5=HIGH, GPIO1=HIGH)
- **红色快闪**: 工厂重置进行中

#### 2. 按键功能

##### 短按GPIO6
- **首次按下**: 激活GPIO6模式
  - GPIO5 = HIGH
  - GPIO1 = LOW
  - LED显示绿色常亮
- **再次按下**: 退出GPIO6模式
  - GPIO5 = LOW
  - GPIO1 = LOW
  - LED恢复到设备状态对应颜色

##### 短按GPIO7
- **首次按下**: 激活GPIO7模式
  - GPIO5 = HIGH
  - GPIO1 = HIGH
  - LED显示蓝色常亮
- **再次按下**: 退出GPIO7模式
  - GPIO5 = LOW
  - GPIO1 = LOW
  - LED恢复到设备状态对应颜色

##### 长按任意按键10秒
- 触发工厂重置功能
- LED快速闪烁红色
- 执行 `factory_reset()` 函数
- 自动重启设备

#### 3. 设备状态自动检测
- 监听Tuya IoT事件
- 自动更新LED状态：
  - `TUYA_EVENT_BIND_START`: 设置为未配网状态（红色呼吸灯）
  - `TUYA_EVENT_MQTT_CONNECTED`: 设置为已配网状态（白色呼吸灯）

#### 4. Shipping模式支持
- 在shipping模式下自动暂停LED和按键控制
- 避免与shipping.cpp中的按键处理冲突
- 退出shipping模式后自动恢复正常功能

## 代码架构

### 核心文件

#### 1. `led_button_control.h/cpp`
- LED和按键控制的核心实现
- 处理所有LED效果和按键逻辑
- 支持shipping模式切换

#### 2. `led_button_task.h/cpp`
- FreeRTOS任务管理
- 提供线程安全的接口
- 任务生命周期管理

#### 3. 集成到 `main.cpp`
- 在setup()中启动LED按键控制任务
- 在事件处理函数中更新设备状态
- 处理shipping模式的切换

### 任务配置
- **任务名称**: `led_button_task`
- **堆栈大小**: 4096字节
- **优先级**: 5
- **更新频率**: 10ms

## 使用方法

### 1. 基本使用
系统会自动启动，无需手动配置。LED会根据设备状态自动显示相应颜色。

### 2. 按键操作
- **短按GPIO6**: 控制GPIO5输出，LED显示绿色
- **短按GPIO7**: 控制GPIO5和GPIO1输出，LED显示蓝色
- **长按任意按键10秒**: 执行工厂重置

### 3. 状态查询
```cpp
// 检查当前按键状态
button_state_t state = led_button_task_get_state();

// 检查是否正在工厂重置
bool resetting = led_button_task_is_factory_resetting();

// 检查是否处于shipping模式
bool shipping = led_button_task_is_shipping_mode();
```

### 4. 手动控制
```cpp
// 设置设备配网状态
led_button_task_set_device_state(true);  // 已配网
led_button_task_set_device_state(false); // 未配网

// 设置shipping模式
led_button_task_set_shipping_mode(true);  // 进入shipping模式
led_button_task_set_shipping_mode(false); // 退出shipping模式
```

## LED效果说明

### 呼吸灯效果
- 亮度从10逐渐增加到255，再从255逐渐减少到10
- 更新频率：20ms
- 用于未配网（红色）和已配网（白色）状态

### 快速闪烁效果
- 200ms开，200ms关
- 用于工厂重置过程中的红色闪烁

### 常亮效果
- 固定亮度显示
- 用于按键激活状态（绿色/蓝色）

## 注意事项

### 1. 与shipping.cpp的兼容性
- 系统自动处理shipping模式切换
- 在shipping模式下，LED按键控制会暂停
- 避免了GPIO冲突问题

### 2. 工厂重置安全性
- 需要长按10秒才能触发
- 过程中LED会明显闪烁提示
- 松开按键会取消重置过程

### 3. 实时响应
- 使用FreeRTOS任务确保实时性
- 10ms更新频率保证按键和LED同步
- OneButton库提供可靠的按键去抖

### 4. 资源使用
- 任务堆栈：4096字节
- 一个WS2812B LED
- 两个GPIO输入（带内部上拉）
- 两个GPIO输出

## 故障排除

### LED不亮
1. 检查GPIO10连接
2. 确认FastLED库正确安装
3. 检查任务是否正常启动

### 按键无响应
1. 检查GPIO6/GPIO7连接
2. 确认OneButton库正确安装
3. 检查是否处于shipping模式

### GPIO输出不正常
1. 检查GPIO5/GPIO1连接
2. 使用万用表测量输出电平
3. 检查按键状态是否正确切换

## 扩展功能

### 添加新的LED颜色
在 `led_color_t` 枚举中添加新颜色，并在 `update_led_display()` 函数中处理。

### 添加新的LED效果
在 `led_effect_t` 枚举中添加新效果，并在 `update_led_display()` 函数中实现逻辑。

### 修改按键行为
修改 `button6_click()` 和 `button7_click()` 函数中的逻辑。

### 调整工厂重置时间
修改 `FACTORY_RESET_TIME` 宏定义（单位：毫秒）。
