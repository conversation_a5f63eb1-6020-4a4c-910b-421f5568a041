/**
 * @file led_button_task.cpp
 * @brief LED和按键控制FreeRTOS任务
 */

#include "led_button_control.h"
#include "tuya_log.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"

// 任务配置
#define LED_BUTTON_TASK_STACK_SIZE  4096
#define LED_BUTTON_TASK_PRIORITY    5
#define LED_BUTTON_TASK_DELAY_MS    10

// 任务句柄
static TaskHandle_t led_button_task_handle = NULL;
static bool task_running = false;

// FreeRTOS任务函数
static void led_button_task_function(void *pvParameters) {
    TY_LOGI("LED Button task started");
    
    // 初始化LED和按键控制
    led_button_init();
    
    task_running = true;
    
    while (task_running) {
        // 处理LED和按键逻辑
        led_button_task();
        
        // 短暂延迟，避免CPU占用过高
        vTaskDelay(pdMS_TO_TICKS(LED_BUTTON_TASK_DELAY_MS));
    }
    
    // 清理资源
    led_button_deinit();
    
    TY_LOGI("LED Button task stopped");
    
    // 删除任务
    led_button_task_handle = NULL;
    vTaskDelete(NULL);
}

/**
 * @brief 启动LED和按键控制任务
 * @return true 启动成功, false 启动失败
 */
bool led_button_task_start(void) {
    if (led_button_task_handle != NULL) {
        TY_LOGW("LED Button task already running");
        return true;
    }
    
    TY_LOGI("Starting LED Button task...");
    
    BaseType_t result = xTaskCreate(
        led_button_task_function,       // 任务函数
        "led_button_task",              // 任务名称
        LED_BUTTON_TASK_STACK_SIZE,     // 堆栈大小
        NULL,                           // 任务参数
        LED_BUTTON_TASK_PRIORITY,       // 任务优先级
        &led_button_task_handle         // 任务句柄
    );
    
    if (result == pdPASS) {
        TY_LOGI("LED Button task started successfully");
        return true;
    } else {
        TY_LOGE("Failed to start LED Button task");
        return false;
    }
}

/**
 * @brief 停止LED和按键控制任务
 */
void led_button_task_stop(void) {
    if (led_button_task_handle == NULL) {
        TY_LOGW("LED Button task not running");
        return;
    }
    
    TY_LOGI("Stopping LED Button task...");
    
    // 设置停止标志
    task_running = false;
    
    // 等待任务结束
    while (led_button_task_handle != NULL) {
        vTaskDelay(pdMS_TO_TICKS(10));
    }
    
    TY_LOGI("LED Button task stopped successfully");
}

/**
 * @brief 检查LED和按键控制任务是否正在运行
 * @return true 正在运行, false 未运行
 */
bool led_button_task_is_running(void) {
    return (led_button_task_handle != NULL && task_running);
}

/**
 * @brief 设置设备配网状态（任务安全版本）
 * @param configured true=已配网, false=未配网
 */
void led_button_task_set_device_state(bool configured) {
    if (led_button_task_is_running()) {
        led_button_set_device_state(configured);
    }
}

/**
 * @brief 获取当前按键状态（任务安全版本）
 * @return button_state_t 当前按键状态
 */
button_state_t led_button_task_get_state(void) {
    if (led_button_task_is_running()) {
        return led_button_get_state();
    }
    return BUTTON_STATE_IDLE;
}

/**
 * @brief 检查是否正在进行工厂重置（任务安全版本）
 * @return true=正在工厂重置, false=正常状态
 */
bool led_button_task_is_factory_resetting(void) {
    if (led_button_task_is_running()) {
        return led_button_is_factory_resetting();
    }
    return false;
}

/**
 * @brief 设置shipping模式状态（任务安全版本）
 * @param shipping_mode true=进入shipping模式, false=退出shipping模式
 */
void led_button_task_set_shipping_mode(bool shipping_mode) {
    if (led_button_task_is_running()) {
        led_button_set_shipping_mode(shipping_mode);
    }
}

/**
 * @brief 检查是否处于shipping模式（任务安全版本）
 * @return true=shipping模式, false=正常模式
 */
bool led_button_task_is_shipping_mode(void) {
    if (led_button_task_is_running()) {
        return led_button_is_shipping_mode();
    }
    return false;
}
