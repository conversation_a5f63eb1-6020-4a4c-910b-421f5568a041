/**
 * @file led_button_control.cpp
 * @brief LED和按键控制模块实现
 */

#include "led_button_control.h"
#include "tuya_log.h"
#include "shipping.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"

// 全局变量
static CRGB leds[NUM_LEDS];
static OneButton button6(BUTTON_GPIO6, true, true);  // active LOW, enable pullup
static OneButton button7(BUTTON_GPIO7, true, true);  // active LOW, enable pullup

// 状态变量
static button_state_t current_button_state = BUTTON_STATE_IDLE;
static device_state_t current_device_state = DEVICE_STATE_UNCONFIGURED;
static bool factory_reset_in_progress = false;
static unsigned long factory_reset_start_time = 0;

// LED控制变量
static led_color_t current_led_color = LED_COLOR_RED;
static led_effect_t current_led_effect = LED_EFFECT_BREATHING;
static unsigned long last_led_update = 0;
static uint8_t breathing_brightness = 0;
static bool breathing_direction = true;
static bool blink_state = false;

// 前向声明
static void update_led_display(void);
static void set_gpio_outputs(gpio_state_t gpio5, gpio_state_t gpio1);
static void handle_factory_reset_progress(void);

// 按键回调函数
static void button6_click(void) {
    TY_LOGI("Button GPIO6 clicked");

    if (factory_reset_in_progress) {
        return; // 工厂重置过程中忽略点击
    }
    
    if (current_button_state == BUTTON_STATE_GPIO6_ACTIVE) {
        // 当前是GPIO6激活状态，切换到空闲状态
        current_button_state = BUTTON_STATE_IDLE;
        set_gpio_outputs(GPIO_STATE_LOW, GPIO_STATE_LOW);
        
        // 恢复到设备状态对应的LED显示
        if (current_device_state == DEVICE_STATE_CONFIGURED) {
            led_button_set_led(LED_COLOR_WHITE, LED_EFFECT_BREATHING);
        } else {
            led_button_set_led(LED_COLOR_RED, LED_EFFECT_BREATHING);
        }
        
        TY_LOGI("GPIO6 deactivated - GPIO5=LOW, GPIO1=LOW");
    } else {
        // 切换到GPIO6激活状态
        current_button_state = BUTTON_STATE_GPIO6_ACTIVE;
        set_gpio_outputs(GPIO_STATE_HIGH, GPIO_STATE_LOW);
        led_button_set_led(LED_COLOR_GREEN, LED_EFFECT_SOLID);
        
        TY_LOGI("GPIO6 activated - GPIO5=HIGH, GPIO1=LOW, LED=GREEN");
    }
}

static void button7_click(void) {
    TY_LOGI("Button GPIO7 clicked");

    if (factory_reset_in_progress) {
        return; // 工厂重置过程中忽略点击
    }
    
    if (current_button_state == BUTTON_STATE_GPIO7_ACTIVE) {
        // 当前是GPIO7激活状态，切换到空闲状态
        current_button_state = BUTTON_STATE_IDLE;
        set_gpio_outputs(GPIO_STATE_LOW, GPIO_STATE_LOW);
        
        // 恢复到设备状态对应的LED显示
        if (current_device_state == DEVICE_STATE_CONFIGURED) {
            led_button_set_led(LED_COLOR_WHITE, LED_EFFECT_BREATHING);
        } else {
            led_button_set_led(LED_COLOR_RED, LED_EFFECT_BREATHING);
        }
        
        TY_LOGI("GPIO7 deactivated - GPIO5=LOW, GPIO1=LOW");
    } else {
        // 切换到GPIO7激活状态
        current_button_state = BUTTON_STATE_GPIO7_ACTIVE;
        set_gpio_outputs(GPIO_STATE_HIGH, GPIO_STATE_HIGH);
        led_button_set_led(LED_COLOR_BLUE, LED_EFFECT_SOLID);
        
        TY_LOGI("GPIO7 activated - GPIO5=HIGH, GPIO1=HIGH, LED=BLUE");
    }
}

static void button6_longpress_start(void) {
    TY_LOGI("Button GPIO6 long press started - factory reset initiated");
    factory_reset_in_progress = true;
    factory_reset_start_time = millis();
    led_button_set_led(LED_COLOR_RED, LED_EFFECT_FAST_BLINK);
}

static void button7_longpress_start(void) {
    TY_LOGI("Button GPIO7 long press started - factory reset initiated");
    factory_reset_in_progress = true;
    factory_reset_start_time = millis();
    led_button_set_led(LED_COLOR_RED, LED_EFFECT_FAST_BLINK);
}

static void button6_longpress_stop(void) {
    TY_LOGI("Button GPIO6 long press stopped");
    if (factory_reset_in_progress && 
        (millis() - factory_reset_start_time) < FACTORY_RESET_TIME) {
        // 长按时间不够，取消工厂重置
        led_button_stop_factory_reset();
    }
}

static void button7_longpress_stop(void) {
    TY_LOGI("Button GPIO7 long press stopped");
    if (factory_reset_in_progress && 
        (millis() - factory_reset_start_time) < FACTORY_RESET_TIME) {
        // 长按时间不够，取消工厂重置
        led_button_stop_factory_reset();
    }
}

// 初始化函数
void led_button_init(void) {
    TY_LOGI("Initializing LED and button control...");
    
    // 初始化FastLED
    FastLED.addLeds<WS2812B, LED_PIN, GRB>(leds, NUM_LEDS);
    FastLED.setBrightness(LED_BRIGHTNESS);
    FastLED.clear();
    FastLED.show();
    
    // 初始化GPIO输出引脚
    pinMode(OUTPUT_GPIO5, OUTPUT);
    pinMode(OUTPUT_GPIO1, OUTPUT);
    digitalWrite(OUTPUT_GPIO5, LOW);
    digitalWrite(OUTPUT_GPIO1, LOW);
    
    // 配置按键
    button6.attachClick(button6_click);
    button6.attachLongPressStart(button6_longpress_start);
    button6.attachLongPressStop(button6_longpress_stop);
    button6.setPressMs(FACTORY_RESET_TIME);
    button6.setDebounceMs(DEBOUNCE_TIME);
    
    button7.attachClick(button7_click);
    button7.attachLongPressStart(button7_longpress_start);
    button7.attachLongPressStop(button7_longpress_stop);
    button7.setPressMs(FACTORY_RESET_TIME);
    button7.setDebounceMs(DEBOUNCE_TIME);
    
    // 设置初始LED状态（未配网状态）
    led_button_set_led(LED_COLOR_RED, LED_EFFECT_BREATHING);
    
    TY_LOGI("LED and button control initialized successfully");
}

void led_button_deinit(void) {
    TY_LOGI("Deinitializing LED and button control...");
    
    // 关闭LED
    FastLED.clear();
    FastLED.show();
    
    // 设置GPIO为低电平
    digitalWrite(OUTPUT_GPIO5, LOW);
    digitalWrite(OUTPUT_GPIO1, LOW);
    
    // 重置状态
    current_button_state = BUTTON_STATE_IDLE;
    factory_reset_in_progress = false;
    
    TY_LOGI("LED and button control deinitialized");
}

void led_button_task(void) {
    // 处理按键事件
    button6.tick();
    button7.tick();

    // 处理工厂重置进度
    handle_factory_reset_progress();

    // 更新LED显示
    update_led_display();
}

void led_button_set_device_state(bool configured) {
    device_state_t new_state = configured ? DEVICE_STATE_CONFIGURED : DEVICE_STATE_UNCONFIGURED;
    
    if (new_state != current_device_state) {
        current_device_state = new_state;
        TY_LOGI("Device state changed to: %s", 
                configured ? "CONFIGURED" : "UNCONFIGURED");
        
        // 如果当前没有按键激活且没有工厂重置，更新LED状态
        if (current_button_state == BUTTON_STATE_IDLE && !factory_reset_in_progress) {
            if (configured) {
                led_button_set_led(LED_COLOR_WHITE, LED_EFFECT_BREATHING);
            } else {
                led_button_set_led(LED_COLOR_RED, LED_EFFECT_BREATHING);
            }
        }
    }
}

button_state_t led_button_get_state(void) {
    return current_button_state;
}

void led_button_set_led(led_color_t color, led_effect_t effect) {
    current_led_color = color;
    current_led_effect = effect;
    
    // 重置LED效果状态
    breathing_brightness = 0;
    breathing_direction = true;
    blink_state = false;
    last_led_update = millis();
}

void led_button_set_gpio_output(gpio_state_t gpio5_state, gpio_state_t gpio1_state) {
    set_gpio_outputs(gpio5_state, gpio1_state);
}

bool led_button_is_factory_resetting(void) {
    return factory_reset_in_progress;
}

void led_button_stop_factory_reset(void) {
    if (factory_reset_in_progress) {
        TY_LOGI("Factory reset cancelled");
        factory_reset_in_progress = false;

        // 恢复到之前的状态
        if (current_button_state == BUTTON_STATE_IDLE) {
            if (current_device_state == DEVICE_STATE_CONFIGURED) {
                led_button_set_led(LED_COLOR_WHITE, LED_EFFECT_BREATHING);
            } else {
                led_button_set_led(LED_COLOR_RED, LED_EFFECT_BREATHING);
            }
        } else if (current_button_state == BUTTON_STATE_GPIO6_ACTIVE) {
            led_button_set_led(LED_COLOR_GREEN, LED_EFFECT_SOLID);
        } else if (current_button_state == BUTTON_STATE_GPIO7_ACTIVE) {
            led_button_set_led(LED_COLOR_BLUE, LED_EFFECT_SOLID);
        }
    }
}

// 内部辅助函数
static void set_gpio_outputs(gpio_state_t gpio5, gpio_state_t gpio1) {
    digitalWrite(OUTPUT_GPIO5, gpio5 == GPIO_STATE_HIGH ? HIGH : LOW);
    digitalWrite(OUTPUT_GPIO1, gpio1 == GPIO_STATE_HIGH ? HIGH : LOW);
    
    TY_LOGD("GPIO outputs set - GPIO5: %s, GPIO1: %s",
            gpio5 == GPIO_STATE_HIGH ? "HIGH" : "LOW",
            gpio1 == GPIO_STATE_HIGH ? "HIGH" : "LOW");
}

static void handle_factory_reset_progress(void) {
    if (!factory_reset_in_progress) {
        return;
    }
    
    unsigned long elapsed = millis() - factory_reset_start_time;
    
    if (elapsed >= FACTORY_RESET_TIME) {
        // 工厂重置时间到，执行重置
        TY_LOGI("Factory reset time reached - executing factory reset");
        
        // 设置LED为快速闪烁红色表示正在重置
        led_button_set_led(LED_COLOR_RED, LED_EFFECT_FAST_BLINK);
        
        // 执行工厂重置
        factory_reset();
        
        // 延迟一段时间让用户看到LED效果
        vTaskDelay(pdMS_TO_TICKS(2000));
        
        // 重启设备
        TY_LOGI("Restarting device after factory reset...");
        ESP.restart();
    }
}

static void update_led_display(void) {
    unsigned long current_time = millis();

    // 根据当前LED颜色设置基础颜色
    CRGB base_color = CRGB::Black;
    switch (current_led_color) {
        case LED_COLOR_RED:
            base_color = CRGB::Red;
            break;
        case LED_COLOR_WHITE:
            base_color = CRGB::White;
            break;
        case LED_COLOR_GREEN:
            base_color = CRGB::Green;
            break;
        case LED_COLOR_BLUE:
            base_color = CRGB::Blue;
            break;
        case LED_COLOR_OFF:
        default:
            base_color = CRGB::Black;
            break;
    }

    // 根据LED效果处理显示
    switch (current_led_effect) {
        case LED_EFFECT_SOLID:
            // 常亮
            leds[0] = base_color;
            break;

        case LED_EFFECT_BREATHING:
            // 呼吸灯效果 - 每20ms更新一次
            if (current_time - last_led_update >= 20) {
                last_led_update = current_time;

                if (breathing_direction) {
                    breathing_brightness += 2;
                    if (breathing_brightness >= 255) {
                        breathing_brightness = 255;
                        breathing_direction = false;
                    }
                } else {
                    breathing_brightness -= 2;
                    if (breathing_brightness <= 10) {
                        breathing_brightness = 10;
                        breathing_direction = true;
                    }
                }

                // 应用呼吸效果
                leds[0] = base_color;
                leds[0].fadeToBlackBy(255 - breathing_brightness);
            }
            break;

        case LED_EFFECT_FAST_BLINK:
            // 快速闪烁 - 每200ms切换一次
            if (current_time - last_led_update >= 200) {
                last_led_update = current_time;
                blink_state = !blink_state;

                if (blink_state) {
                    leds[0] = base_color;
                } else {
                    leds[0] = CRGB::Black;
                }
            }
            break;
    }

    // 更新LED显示
    FastLED.show();
}
