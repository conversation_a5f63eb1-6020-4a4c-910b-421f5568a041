/**
 * @file led_button_control.h
 * @brief LED和按键控制模块
 * 
 * 功能说明：
 * 1. 控制WS2812B LED显示不同颜色和效果
 * 2. 处理GPIO6和GPIO7按键事件
 * 3. 控制GPIO5和GPIO1输出
 * 4. 实现工厂重置功能
 */

#ifndef LED_BUTTON_CONTROL_H
#define LED_BUTTON_CONTROL_H

#include <Arduino.h>
#include <OneButton.h>
#include <FastLED.h>

#ifdef __cplusplus
extern "C" {
#endif

// GPIO定义
#define LED_PIN         10      // WS2812B LED引脚
#define BUTTON_GPIO6    6       // 按键6
#define BUTTON_GPIO7    7       // 按键7
#define OUTPUT_GPIO5    5       // 输出引脚5
#define OUTPUT_GPIO1    1       // 输出引脚1

// LED配置
#define NUM_LEDS        1       // LED数量
#define LED_BRIGHTNESS  128     // LED亮度 (0-255)

// 按键配置
#define FACTORY_RESET_TIME  10000   // 工厂重置长按时间(毫秒)
#define DEBOUNCE_TIME       50      // 去抖时间(毫秒)

// LED颜色定义
typedef enum {
    LED_COLOR_OFF = 0,      // 关闭
    LED_COLOR_RED,          // 红色
    LED_COLOR_WHITE,        // 白色
    LED_COLOR_GREEN,        // 绿色
    LED_COLOR_BLUE          // 蓝色
} led_color_t;

// LED效果定义
typedef enum {
    LED_EFFECT_SOLID = 0,   // 常亮
    LED_EFFECT_BREATHING,   // 呼吸灯
    LED_EFFECT_FAST_BLINK   // 快速闪烁
} led_effect_t;

// GPIO状态定义
typedef enum {
    GPIO_STATE_LOW = 0,     // 低电平
    GPIO_STATE_HIGH = 1     // 高电平
} gpio_state_t;

// 按键状态定义
typedef enum {
    BUTTON_STATE_IDLE = 0,      // 空闲状态
    BUTTON_STATE_GPIO6_ACTIVE,  // GPIO6激活状态
    BUTTON_STATE_GPIO7_ACTIVE   // GPIO7激活状态
} button_state_t;

// 设备状态定义
typedef enum {
    DEVICE_STATE_UNCONFIGURED = 0,  // 未配网
    DEVICE_STATE_CONFIGURED         // 已配网
} device_state_t;

/**
 * @brief 初始化LED和按键控制模块
 */
void led_button_init(void);

/**
 * @brief 反初始化LED和按键控制模块
 */
void led_button_deinit(void);

/**
 * @brief LED和按键控制任务处理函数
 * 需要在主循环或FreeRTOS任务中定期调用
 */
void led_button_task(void);

/**
 * @brief 设置设备配网状态
 * @param configured true=已配网, false=未配网
 */
void led_button_set_device_state(bool configured);

/**
 * @brief 获取当前按键状态
 * @return button_state_t 当前按键状态
 */
button_state_t led_button_get_state(void);

/**
 * @brief 设置LED颜色和效果
 * @param color LED颜色
 * @param effect LED效果
 */
void led_button_set_led(led_color_t color, led_effect_t effect);

/**
 * @brief 设置GPIO输出状态
 * @param gpio5_state GPIO5状态
 * @param gpio1_state GPIO1状态
 */
void led_button_set_gpio_output(gpio_state_t gpio5_state, gpio_state_t gpio1_state);

/**
 * @brief 检查是否正在进行工厂重置
 * @return true=正在工厂重置, false=正常状态
 */
bool led_button_is_factory_resetting(void);

/**
 * @brief 强制停止工厂重置过程
 */
void led_button_stop_factory_reset(void);

/**
 * @brief 设置shipping模式状态
 * @param shipping_mode true=进入shipping模式, false=退出shipping模式
 * @note 在shipping模式下，LED和按键控制会被暂停
 */
void led_button_set_shipping_mode(bool shipping_mode);

/**
 * @brief 检查是否处于shipping模式
 * @return true=shipping模式, false=正常模式
 */
bool led_button_is_shipping_mode(void);

#ifdef __cplusplus
}
#endif

#endif /* LED_BUTTON_CONTROL_H */
