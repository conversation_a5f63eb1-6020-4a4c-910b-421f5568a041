/**
 * @file LED_BUTTON_TEST_EXAMPLE.cpp
 * @brief LED和按键控制系统测试示例
 * 
 * 这个文件展示了如何使用LED和按键控制系统的各种功能
 * 注意：这只是示例代码，不需要编译到项目中
 */

#include "led_button_task.h"
#include "tuya_log.h"

// 测试函数：基本功能测试
void test_basic_functionality() {
    TY_LOGI("=== LED Button Control Basic Test ===");
    
    // 1. 启动LED按键控制任务
    if (led_button_task_start()) {
        TY_LOGI("✓ LED Button task started successfully");
    } else {
        TY_LOGE("✗ Failed to start LED Button task");
        return;
    }
    
    // 2. 检查任务状态
    if (led_button_task_is_running()) {
        TY_LOGI("✓ LED Button task is running");
    }
    
    // 3. 测试设备状态设置
    TY_LOGI("Testing device state changes...");
    
    // 设置为未配网状态（红色呼吸灯）
    led_button_task_set_device_state(false);
    TY_LOGI("✓ Set device state to UNCONFIGURED (red breathing)");
    vTaskDelay(pdMS_TO_TICKS(3000)); // 等待3秒观察效果
    
    // 设置为已配网状态（白色呼吸灯）
    led_button_task_set_device_state(true);
    TY_LOGI("✓ Set device state to CONFIGURED (white breathing)");
    vTaskDelay(pdMS_TO_TICKS(3000)); // 等待3秒观察效果
    
    TY_LOGI("=== Basic Test Completed ===");
}

// 测试函数：按键状态监控
void test_button_monitoring() {
    TY_LOGI("=== Button State Monitoring Test ===");
    TY_LOGI("Please press buttons to test functionality...");
    TY_LOGI("- Short press GPIO6: Green LED, GPIO5=HIGH, GPIO1=LOW");
    TY_LOGI("- Short press GPIO7: Blue LED, GPIO5=HIGH, GPIO1=HIGH");
    TY_LOGI("- Long press (10s): Factory reset with red blinking");
    
    button_state_t last_state = BUTTON_STATE_IDLE;
    
    // 监控按键状态变化30秒
    for (int i = 0; i < 300; i++) { // 30秒，每100ms检查一次
        button_state_t current_state = led_button_task_get_state();
        
        // 状态发生变化时打印信息
        if (current_state != last_state) {
            switch (current_state) {
                case BUTTON_STATE_IDLE:
                    TY_LOGI("Button state: IDLE (GPIO5=LOW, GPIO1=LOW)");
                    break;
                case BUTTON_STATE_GPIO6_ACTIVE:
                    TY_LOGI("Button state: GPIO6 ACTIVE (GPIO5=HIGH, GPIO1=LOW, LED=GREEN)");
                    break;
                case BUTTON_STATE_GPIO7_ACTIVE:
                    TY_LOGI("Button state: GPIO7 ACTIVE (GPIO5=HIGH, GPIO1=HIGH, LED=BLUE)");
                    break;
            }
            last_state = current_state;
        }
        
        // 检查工厂重置状态
        if (led_button_task_is_factory_resetting()) {
            TY_LOGW("⚠️  Factory reset in progress! LED should be blinking red.");
        }
        
        vTaskDelay(pdMS_TO_TICKS(100));
    }
    
    TY_LOGI("=== Button Monitoring Test Completed ===");
}

// 测试函数：Shipping模式测试
void test_shipping_mode() {
    TY_LOGI("=== Shipping Mode Test ===");
    
    // 1. 正常模式
    TY_LOGI("Normal mode - LED should show device state");
    led_button_task_set_device_state(false); // 红色呼吸灯
    vTaskDelay(pdMS_TO_TICKS(2000));
    
    // 2. 进入shipping模式
    TY_LOGI("Entering shipping mode - LED should turn off");
    led_button_task_set_shipping_mode(true);
    
    if (led_button_task_is_shipping_mode()) {
        TY_LOGI("✓ Successfully entered shipping mode");
        TY_LOGI("LED should be OFF, buttons should be inactive");
    }
    
    vTaskDelay(pdMS_TO_TICKS(5000)); // 等待5秒
    
    // 3. 退出shipping模式
    TY_LOGI("Exiting shipping mode - LED should resume normal operation");
    led_button_task_set_shipping_mode(false);
    
    if (!led_button_task_is_shipping_mode()) {
        TY_LOGI("✓ Successfully exited shipping mode");
        TY_LOGI("LED should show red breathing (unconfigured state)");
    }
    
    vTaskDelay(pdMS_TO_TICKS(3000));
    
    TY_LOGI("=== Shipping Mode Test Completed ===");
}

// 测试函数：完整系统测试
void test_complete_system() {
    TY_LOGI("=== Complete System Test ===");
    
    // 1. 基本功能测试
    test_basic_functionality();
    vTaskDelay(pdMS_TO_TICKS(1000));
    
    // 2. Shipping模式测试
    test_shipping_mode();
    vTaskDelay(pdMS_TO_TICKS(1000));
    
    // 3. 按键监控测试
    test_button_monitoring();
    
    TY_LOGI("=== All Tests Completed ===");
}

// 测试函数：性能和资源使用测试
void test_performance() {
    TY_LOGI("=== Performance Test ===");
    
    if (!led_button_task_is_running()) {
        TY_LOGE("LED Button task not running, cannot perform performance test");
        return;
    }
    
    // 获取任务信息
    TaskHandle_t task_handle = xTaskGetHandle("led_button_task");
    if (task_handle != NULL) {
        UBaseType_t stack_high_water_mark = uxTaskGetStackHighWaterMark(task_handle);
        TY_LOGI("LED Button task stack high water mark: %u bytes", stack_high_water_mark);
        
        if (stack_high_water_mark < 512) {
            TY_LOGW("⚠️  Stack usage is high, consider increasing stack size");
        } else {
            TY_LOGI("✓ Stack usage is normal");
        }
    }
    
    // 测试响应时间
    TY_LOGI("Testing LED update responsiveness...");
    unsigned long start_time = millis();
    
    // 快速切换设备状态，测试响应性
    for (int i = 0; i < 10; i++) {
        led_button_task_set_device_state(i % 2 == 0);
        vTaskDelay(pdMS_TO_TICKS(100));
    }
    
    unsigned long end_time = millis();
    TY_LOGI("State change test completed in %lu ms", end_time - start_time);
    
    TY_LOGI("=== Performance Test Completed ===");
}

// 主测试函数
void run_led_button_tests() {
    TY_LOGI("Starting LED Button Control System Tests...");
    TY_LOGI("Make sure hardware is connected:");
    TY_LOGI("- WS2812B LED on GPIO10");
    TY_LOGI("- Buttons on GPIO6 and GPIO7");
    TY_LOGI("- Monitor GPIO5 and GPIO1 outputs");
    
    vTaskDelay(pdMS_TO_TICKS(2000)); // 等待2秒让用户准备
    
    // 运行所有测试
    test_complete_system();
    
    // 性能测试
    test_performance();
    
    TY_LOGI("All LED Button Control tests completed!");
}

/**
 * 使用说明：
 * 
 * 1. 在main.cpp的setup()函数中调用 run_led_button_tests() 来运行测试
 * 2. 或者在CLI中添加测试命令
 * 3. 观察串口输出和LED行为
 * 4. 测试按键功能
 * 
 * 示例集成到main.cpp：
 * 
 * void setup() {
 *     // ... 其他初始化代码 ...
 *     
 *     // 运行LED按键测试（可选）
 *     #ifdef LED_BUTTON_TEST
 *     run_led_button_tests();
 *     #endif
 * }
 * 
 * 或者添加到CLI命令：
 * 
 * else if (strcmp(command, "test led") == 0) {
 *     run_led_button_tests();
 * }
 */
